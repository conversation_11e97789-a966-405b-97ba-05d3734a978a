import discord
from discord.ext import commands
from discord import app_commands
import logging
from typing import Optional, Literal

logger = logging.getLogger(__name__)

class CommandsCog(commands.Cog):
    """Cog for handling slash commands"""

    def __init__(self, bot):
        self.bot = bot

    @app_commands.command(name="record", description="Start recording a voice channel")
    @app_commands.describe(
        channel="Voice channel to record (defaults to your current channel)",
        format="Audio format for the recording",
        duration="Maximum recording duration in seconds (0 for unlimited)"
    )
    async def record(
        self,
        interaction: discord.Interaction,
        channel: Optional[discord.VoiceChannel] = None,
        format: Optional[Literal["mp3", "wav"]] = None,
        duration: Optional[int] = None
    ):
        """Start recording a voice channel"""
        await interaction.response.defer()

        try:
            # Determine target channel
            target_channel = channel
            if not target_channel:
                # Check if user is in a voice channel
                if interaction.user.voice and interaction.user.voice.channel:
                    target_channel = interaction.user.voice.channel
                else:
                    await interaction.followup.send("❌ You must be in a voice channel or specify one!", ephemeral=True)
                    return

            # Check permissions
            if not interaction.user.guild_permissions.manage_channels:
                await interaction.followup.send("❌ You need 'Manage Channels' permission to start recordings!", ephemeral=True)
                return

            # Validate duration
            if duration is not None:
                if duration < 0:
                    await interaction.followup.send("❌ Duration cannot be negative!", ephemeral=True)
                    return
                if duration > self.bot.max_duration:
                    await interaction.followup.send(f"❌ Duration cannot exceed {self.bot.max_duration} seconds!", ephemeral=True)
                    return

            # Get recording cog
            recording_cog = self.bot.get_cog('RecordingCog')
            if not recording_cog:
                await interaction.followup.send("❌ Recording system not available!", ephemeral=True)
                return

            # Start recording
            success = await recording_cog.start_recording(target_channel, interaction, format, duration)

        except Exception as e:
            logger.error(f"Error in record command: {str(e)}")
            await interaction.followup.send(f"❌ An error occurred: {str(e)}", ephemeral=True)

    @app_commands.command(name="stop", description="Stop recording in a voice channel")
    @app_commands.describe(channel="Voice channel to stop recording (defaults to your current channel)")
    async def stop(
        self,
        interaction: discord.Interaction,
        channel: Optional[discord.VoiceChannel] = None
    ):
        """Stop recording in a voice channel"""
        await interaction.response.defer()

        try:
            # Determine target channel
            target_channel = channel
            if not target_channel:
                if interaction.user.voice and interaction.user.voice.channel:
                    target_channel = interaction.user.voice.channel
                else:
                    await interaction.followup.send("❌ You must be in a voice channel or specify one!", ephemeral=True)
                    return

            # Check permissions
            if not interaction.user.guild_permissions.manage_channels:
                await interaction.followup.send("❌ You need 'Manage Channels' permission to stop recordings!", ephemeral=True)
                return

            # Get recording cog
            recording_cog = self.bot.get_cog('RecordingCog')
            if not recording_cog:
                await interaction.followup.send("❌ Recording system not available!", ephemeral=True)
                return

            # Stop recording
            success = await recording_cog.stop_recording(target_channel.id, interaction)

        except Exception as e:
            logger.error(f"Error in stop command: {str(e)}")
            await interaction.followup.send(f"❌ An error occurred: {str(e)}", ephemeral=True)

    @app_commands.command(name="status", description="Check recording status")
    async def status(self, interaction: discord.Interaction):
        """Check the status of active recordings"""
        await interaction.response.defer()

        try:
            if not self.bot.active_recordings:
                embed = discord.Embed(
                    title="📊 Recording Status",
                    description="No active recordings",
                    color=discord.Color.blue()
                )
                await interaction.followup.send(embed=embed)
                return

            embed = discord.Embed(
                title="📊 Active Recordings",
                color=discord.Color.green()
            )

            for channel_id, info in self.bot.active_recordings.items():
                channel = self.bot.get_channel(channel_id)
                channel_name = channel.name if channel else f"Channel {channel_id}"

                duration = (discord.utils.utcnow() - info['start_time']).total_seconds()

                embed.add_field(
                    name=f"🎙️ {channel_name}",
                    value=f"**Duration:** {duration:.1f}s\n"
                          f"**Format:** {info['format'].upper()}\n"
                          f"**Participants:** {len(info['participants'])}",
                    inline=True
                )

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"Error in status command: {str(e)}")
            await interaction.followup.send(f"❌ An error occurred: {str(e)}", ephemeral=True)

    @app_commands.command(name="consent", description="Give or revoke consent for voice recordings")
    @app_commands.describe(consent="Whether you consent to being recorded")
    async def consent(
        self,
        interaction: discord.Interaction,
        consent: bool
    ):
        """Give or revoke consent for voice recordings"""
        await interaction.response.defer(ephemeral=True)

        try:
            self.bot.user_consents[interaction.user.id] = consent

            if consent:
                embed = discord.Embed(
                    title="✅ Consent Given",
                    description="You have consented to voice recordings. You can revoke this at any time.",
                    color=discord.Color.green()
                )
            else:
                embed = discord.Embed(
                    title="❌ Consent Revoked",
                    description="You have revoked consent for voice recordings. You will not be included in future recordings.",
                    color=discord.Color.red()
                )

            await interaction.followup.send(embed=embed, ephemeral=True)
            logger.info(f"User {interaction.user.display_name} set consent to {consent}")

        except Exception as e:
            logger.error(f"Error in consent command: {str(e)}")
            await interaction.followup.send(f"❌ An error occurred: {str(e)}", ephemeral=True)

    @app_commands.command(name="join", description="Join a voice channel")
    @app_commands.describe(channel="Voice channel to join (defaults to your current channel)")
    async def join(
        self,
        interaction: discord.Interaction,
        channel: Optional[discord.VoiceChannel] = None
    ):
        """Join a voice channel"""
        await interaction.response.defer()

        try:
            # Determine target channel
            target_channel = channel
            if not target_channel:
                if interaction.user.voice and interaction.user.voice.channel:
                    target_channel = interaction.user.voice.channel
                else:
                    await interaction.followup.send("❌ You must be in a voice channel or specify one!", ephemeral=True)
                    return

            # Get recording cog
            recording_cog = self.bot.get_cog('RecordingCog')
            if not recording_cog:
                await interaction.followup.send("❌ Recording system not available!", ephemeral=True)
                return

            # Join voice channel
            vc = await recording_cog.join_voice_channel(target_channel, interaction)
            if vc:
                embed = discord.Embed(
                    title="✅ Joined Voice Channel",
                    description=f"Successfully joined **{target_channel.name}**",
                    color=discord.Color.green()
                )
                await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"Error in join command: {str(e)}")
            await interaction.followup.send(f"❌ An error occurred: {str(e)}", ephemeral=True)

    @app_commands.command(name="leave", description="Leave a voice channel")
    @app_commands.describe(channel="Voice channel to leave (defaults to your current channel)")
    async def leave(
        self,
        interaction: discord.Interaction,
        channel: Optional[discord.VoiceChannel] = None
    ):
        """Leave a voice channel"""
        await interaction.response.defer()

        try:
            # Determine target channel
            target_channel = channel
            if not target_channel:
                if interaction.user.voice and interaction.user.voice.channel:
                    target_channel = interaction.user.voice.channel
                else:
                    await interaction.followup.send("❌ You must be in a voice channel or specify one!", ephemeral=True)
                    return

            # Get recording cog
            recording_cog = self.bot.get_cog('RecordingCog')
            if not recording_cog:
                await interaction.followup.send("❌ Recording system not available!", ephemeral=True)
                return

            # Leave voice channel
            success = await recording_cog.leave_voice_channel(target_channel.id, interaction)
            if success:
                embed = discord.Embed(
                    title="👋 Left Voice Channel",
                    description=f"Successfully left **{target_channel.name}**",
                    color=discord.Color.orange()
                )
                await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"Error in leave command: {str(e)}")
            await interaction.followup.send(f"❌ An error occurred: {str(e)}", ephemeral=True)

async def setup(bot):
    await bot.add_cog(CommandsCog(bot))
