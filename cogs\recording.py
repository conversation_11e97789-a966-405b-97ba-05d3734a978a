import discord
from discord.ext import commands
import asyncio
import os
import logging
from datetime import datetime
import wave
import io
from pydub import AudioSegment
import ffmpeg
from typing import Dict, Optional, List
import threading
import time

logger = logging.getLogger(__name__)

class AudioSink(discord.sinks.WaveSink):
    """Custom audio sink for recording voice channel audio"""

    def __init__(self, *, filters=None):
        super().__init__(filters=filters)
        self.audio_data = {}
        self.start_time = datetime.now()

    def write(self, data, user):
        """Write audio data for a specific user"""
        if user not in self.audio_data:
            self.audio_data[user] = io.BytesIO()
        self.audio_data[user].write(data)

    def cleanup(self):
        """Clean up audio data"""
        for user_id, audio_buffer in self.audio_data.items():
            audio_buffer.close()
        self.audio_data.clear()

class RecordingCog(commands.Cog):
    """Cog for handling voice channel recording functionality"""

    def __init__(self, bot):
        self.bot = bot
        self.voice_clients: Dict[int, discord.VoiceClient] = {}
        self.recording_tasks: Dict[int, asyncio.Task] = {}
        self.audio_sinks: Dict[int, AudioSink] = {}

    async def cog_unload(self):
        """Clean up when cog is unloaded"""
        for task in self.recording_tasks.values():
            task.cancel()

        for vc in self.voice_clients.values():
            if vc.is_connected():
                await vc.disconnect()

    async def join_voice_channel(self, channel: discord.VoiceChannel, interaction: discord.Interaction = None) -> Optional[discord.VoiceClient]:
        """Join a voice channel with proper error handling"""
        try:
            # Check permissions
            permissions = channel.permissions_for(channel.guild.me)
            if not permissions.connect:
                error_msg = f"Missing permission to connect to {channel.name}"
                logger.error(error_msg)
                if interaction:
                    await interaction.followup.send(f"❌ {error_msg}", ephemeral=True)
                return None

            if not permissions.speak:
                error_msg = f"Missing permission to speak in {channel.name}"
                logger.error(error_msg)
                if interaction:
                    await interaction.followup.send(f"❌ {error_msg}", ephemeral=True)
                return None

            # Check if already connected to this channel
            if channel.id in self.voice_clients:
                vc = self.voice_clients[channel.id]
                if vc.is_connected():
                    return vc

            # Connect to voice channel
            vc = await channel.connect()
            self.voice_clients[channel.id] = vc

            logger.info(f"Successfully joined voice channel: {channel.name} ({channel.id})")
            return vc

        except discord.ClientException as e:
            error_msg = f"Failed to join voice channel: {str(e)}"
            logger.error(error_msg)
            if interaction:
                await interaction.followup.send(f"❌ {error_msg}", ephemeral=True)
            return None
        except Exception as e:
            error_msg = f"Unexpected error joining voice channel: {str(e)}"
            logger.error(error_msg)
            if interaction:
                await interaction.followup.send(f"❌ {error_msg}", ephemeral=True)
            return None

    async def leave_voice_channel(self, channel_id: int, interaction: discord.Interaction = None) -> bool:
        """Leave a voice channel"""
        try:
            if channel_id in self.voice_clients:
                vc = self.voice_clients[channel_id]
                if vc.is_connected():
                    await vc.disconnect()
                del self.voice_clients[channel_id]

                # Cancel any active recording
                if channel_id in self.recording_tasks:
                    self.recording_tasks[channel_id].cancel()
                    del self.recording_tasks[channel_id]

                # Clean up audio sink
                if channel_id in self.audio_sinks:
                    self.audio_sinks[channel_id].cleanup()
                    del self.audio_sinks[channel_id]

                logger.info(f"Left voice channel: {channel_id}")
                return True

            return False

        except Exception as e:
            error_msg = f"Error leaving voice channel: {str(e)}"
            logger.error(error_msg)
            if interaction:
                await interaction.followup.send(f"❌ {error_msg}", ephemeral=True)
            return False

    def check_user_consent(self, user_id: int) -> bool:
        """Check if user has given consent to be recorded"""
        if not self.bot.require_consent:
            return True
        return self.bot.user_consents.get(user_id, False)

    async def request_user_consent(self, users: List[discord.Member], channel: discord.TextChannel) -> List[int]:
        """Request consent from users to be recorded"""
        if not self.bot.require_consent:
            return [user.id for user in users]

        consented_users = []

        for user in users:
            if user.bot:  # Skip bots
                continue

            if self.bot.user_consents.get(user.id, False):
                consented_users.append(user.id)
                continue

            # Send consent request
            embed = discord.Embed(
                title="🎙️ Recording Consent Required",
                description=f"A voice recording is about to start in a channel you're in. Do you consent to being recorded?",
                color=discord.Color.orange()
            )
            embed.add_field(
                name="Privacy Notice",
                value="Recordings may be stored temporarily and shared with server members. You can revoke consent at any time.",
                inline=False
            )

            view = ConsentView(user.id, self.bot)

            try:
                await user.send(embed=embed, view=view)
                logger.info(f"Sent consent request to {user.display_name}")
            except discord.Forbidden:
                # Can't DM user, ask in channel
                await channel.send(f"{user.mention}, please check your DMs for a recording consent request, or use `/consent` command.")

        return consented_users

    async def start_recording(self, channel: discord.VoiceChannel, interaction: discord.Interaction,
                            format_type: str = None, duration: int = None) -> bool:
        """Start recording a voice channel"""
        try:
            # Join voice channel
            vc = await self.join_voice_channel(channel, interaction)
            if not vc:
                return False

            # Check if already recording
            if channel.id in self.bot.active_recordings:
                await interaction.followup.send("❌ Already recording in this channel!", ephemeral=True)
                return False

            # Get users in channel and request consent
            users_in_channel = [member for member in channel.members if not member.bot]
            if not users_in_channel:
                await interaction.followup.send("❌ No users in voice channel to record!", ephemeral=True)
                return False

            # Request consent from users
            consented_users = await self.request_user_consent(users_in_channel, interaction.channel)

            if not consented_users:
                await interaction.followup.send("❌ No users consented to recording!", ephemeral=True)
                return False

            # Set up recording parameters
            recording_format = format_type or self.bot.default_format
            max_duration = min(duration or self.bot.max_duration, self.bot.max_duration)

            # Create audio sink
            sink = AudioSink()
            self.audio_sinks[channel.id] = sink

            # Start recording
            vc.start_recording(sink, self._recording_finished_callback, channel.id)

            # Track recording info
            self.bot.active_recordings[channel.id] = {
                'start_time': datetime.now(),
                'channel_name': channel.name,
                'guild_id': channel.guild.id,
                'participants': consented_users,
                'format': recording_format,
                'max_duration': max_duration,
                'requester_id': interaction.user.id
            }

            # Schedule automatic stop
            if max_duration > 0:
                task = asyncio.create_task(self._auto_stop_recording(channel.id, max_duration))
                self.recording_tasks[channel.id] = task

            # Send confirmation
            embed = discord.Embed(
                title="🎙️ Recording Started",
                description=f"Started recording in **{channel.name}**",
                color=discord.Color.green(),
                timestamp=datetime.now()
            )
            embed.add_field(name="Format", value=recording_format.upper(), inline=True)
            embed.add_field(name="Max Duration", value=f"{max_duration}s" if max_duration > 0 else "Unlimited", inline=True)
            embed.add_field(name="Participants", value=f"{len(consented_users)} users", inline=True)

            await interaction.followup.send(embed=embed)
            logger.info(f"Started recording in {channel.name} ({channel.id})")
            return True

        except Exception as e:
            error_msg = f"Failed to start recording: {str(e)}"
            logger.error(error_msg)
            await interaction.followup.send(f"❌ {error_msg}", ephemeral=True)
            return False

    async def stop_recording(self, channel_id: int, interaction: discord.Interaction = None) -> bool:
        """Stop recording in a voice channel"""
        try:
            if channel_id not in self.bot.active_recordings:
                if interaction:
                    await interaction.followup.send("❌ No active recording in this channel!", ephemeral=True)
                return False

            # Get voice client
            vc = self.voice_clients.get(channel_id)
            if vc and vc.is_recording():
                vc.stop_recording()

            # Cancel auto-stop task
            if channel_id in self.recording_tasks:
                self.recording_tasks[channel_id].cancel()
                del self.recording_tasks[channel_id]

            recording_info = self.bot.active_recordings[channel_id]
            duration = (datetime.now() - recording_info['start_time']).total_seconds()

            if interaction:
                embed = discord.Embed(
                    title="⏹️ Recording Stopped",
                    description=f"Stopped recording in **{recording_info['channel_name']}**",
                    color=discord.Color.red(),
                    timestamp=datetime.now()
                )
                embed.add_field(name="Duration", value=f"{duration:.1f}s", inline=True)
                embed.add_field(name="Processing", value="Audio files are being processed...", inline=False)

                await interaction.followup.send(embed=embed)

            logger.info(f"Stopped recording in channel {channel_id}")
            return True

        except Exception as e:
            error_msg = f"Failed to stop recording: {str(e)}"
            logger.error(error_msg)
            if interaction:
                await interaction.followup.send(f"❌ {error_msg}", ephemeral=True)
            return False

    async def _auto_stop_recording(self, channel_id: int, duration: int):
        """Automatically stop recording after specified duration"""
        try:
            await asyncio.sleep(duration)
            await self.stop_recording(channel_id)
        except asyncio.CancelledError:
            pass  # Recording was stopped manually

    def _recording_finished_callback(self, sink: AudioSink, channel_id: int):
        """Callback when recording finishes"""
        asyncio.create_task(self._process_recording(sink, channel_id))

    async def _process_recording(self, sink: AudioSink, channel_id: int):
        """Process and save the recorded audio"""
        try:
            if channel_id not in self.bot.active_recordings:
                return

            recording_info = self.bot.active_recordings[channel_id]

            # Process audio files
            await self._save_audio_files(sink, recording_info)

            # Clean up
            del self.bot.active_recordings[channel_id]
            if channel_id in self.audio_sinks:
                del self.audio_sinks[channel_id]

        except Exception as e:
            logger.error(f"Error processing recording: {str(e)}")

    async def _save_audio_files(self, sink: AudioSink, recording_info: dict):
        """Save audio files and send them to the appropriate channel"""
        try:
            from pydub import AudioSegment
            import tempfile

            # Create timestamp for filename
            timestamp = recording_info['start_time'].strftime("%Y%m%d_%H%M%S")
            channel_name = recording_info['channel_name'].replace(' ', '_')

            # Create mixed audio file
            mixed_audio = None
            individual_files = []

            for user_id, audio_data in sink.audio_data.items():
                if user_id not in recording_info['participants']:
                    continue  # Skip users who didn't consent

                try:
                    # Get user info
                    user = self.bot.get_user(user_id)
                    username = user.display_name if user else f"User_{user_id}"
                    username = username.replace(' ', '_')

                    # Convert audio data to AudioSegment
                    audio_data.seek(0)
                    audio_bytes = audio_data.read()

                    if len(audio_bytes) == 0:
                        continue  # Skip empty audio

                    # Create AudioSegment from raw PCM data
                    audio_segment = AudioSegment(
                        data=audio_bytes,
                        sample_width=2,  # 16-bit
                        frame_rate=48000,  # Discord's sample rate
                        channels=2  # Stereo
                    )

                    # Save individual file if requested
                    individual_filename = f"{timestamp}_{channel_name}_{username}.{recording_info['format']}"
                    individual_path = os.path.join(self.bot.recordings_dir, individual_filename)

                    audio_segment.export(individual_path, format=recording_info['format'])
                    individual_files.append((individual_path, username))

                    # Add to mixed audio
                    if mixed_audio is None:
                        mixed_audio = audio_segment
                    else:
                        mixed_audio = mixed_audio.overlay(audio_segment)

                except Exception as e:
                    logger.error(f"Error processing audio for user {user_id}: {str(e)}")
                    continue

            # Save mixed audio file
            mixed_filename = f"{timestamp}_{channel_name}_mixed.{recording_info['format']}"
            mixed_path = os.path.join(self.bot.recordings_dir, mixed_filename)

            if mixed_audio:
                mixed_audio.export(mixed_path, format=recording_info['format'])

            # Send files to Discord
            await self._send_recording_files(recording_info, mixed_path, individual_files)

        except Exception as e:
            logger.error(f"Error saving audio files: {str(e)}")

    async def _send_recording_files(self, recording_info: dict, mixed_path: str, individual_files: list):
        """Send recording files to Discord channel or DM"""
        try:
            # Get the guild and channel
            guild = self.bot.get_guild(recording_info['guild_id'])
            if not guild:
                logger.error(f"Could not find guild {recording_info['guild_id']}")
                return

            # Determine where to send files
            output_channel = None
            if self.bot.default_output_channel:
                output_channel = guild.get_channel(self.bot.default_output_channel)

            if not output_channel:
                # Try to find a general channel
                for channel in guild.text_channels:
                    if channel.name.lower() in ['general', 'recordings', 'voice-recordings']:
                        output_channel = channel
                        break

                if not output_channel:
                    output_channel = guild.text_channels[0]  # Use first available channel

            # Create embed with recording info
            duration = (datetime.now() - recording_info['start_time']).total_seconds()
            embed = discord.Embed(
                title="🎙️ Voice Recording Complete",
                description=f"Recording from **{recording_info['channel_name']}**",
                color=discord.Color.blue(),
                timestamp=recording_info['start_time']
            )
            embed.add_field(name="Duration", value=f"{duration:.1f} seconds", inline=True)
            embed.add_field(name="Format", value=recording_info['format'].upper(), inline=True)
            embed.add_field(name="Participants", value=f"{len(recording_info['participants'])} users", inline=True)

            # Check file sizes and send
            files_to_send = []

            # Check mixed file
            if os.path.exists(mixed_path) and os.path.getsize(mixed_path) < self.bot.max_file_size:
                files_to_send.append(discord.File(mixed_path))

            # Check individual files
            for file_path, username in individual_files:
                if os.path.exists(file_path) and os.path.getsize(file_path) < self.bot.max_file_size:
                    files_to_send.append(discord.File(file_path))

            if files_to_send:
                await output_channel.send(embed=embed, files=files_to_send)
                logger.info(f"Sent {len(files_to_send)} recording files to {output_channel.name}")
            else:
                embed.add_field(
                    name="⚠️ Files Too Large",
                    value="Recording files exceed Discord's upload limit. Files saved locally.",
                    inline=False
                )
                await output_channel.send(embed=embed)

            # Schedule file cleanup
            if self.bot.auto_delete_hours > 0:
                asyncio.create_task(self._cleanup_files_later([mixed_path] + [f[0] for f in individual_files]))

        except Exception as e:
            logger.error(f"Error sending recording files: {str(e)}")

    async def _cleanup_files_later(self, file_paths: list):
        """Clean up recording files after specified time"""
        try:
            await asyncio.sleep(self.bot.auto_delete_hours * 3600)  # Convert hours to seconds

            for file_path in file_paths:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        logger.info(f"Cleaned up recording file: {file_path}")
                except Exception as e:
                    logger.error(f"Error cleaning up file {file_path}: {str(e)}")

        except asyncio.CancelledError:
            pass

class ConsentView(discord.ui.View):
    """View for handling recording consent"""

    def __init__(self, user_id: int, bot):
        super().__init__(timeout=300)  # 5 minute timeout
        self.user_id = user_id
        self.bot = bot

    @discord.ui.button(label="✅ I Consent", style=discord.ButtonStyle.green)
    async def consent_yes(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.user_id:
            await interaction.response.send_message("This consent request is not for you.", ephemeral=True)
            return

        self.bot.user_consents[self.user_id] = True
        await interaction.response.send_message("✅ Thank you! You have consented to voice recording.", ephemeral=True)
        self.stop()

    @discord.ui.button(label="❌ I Do Not Consent", style=discord.ButtonStyle.red)
    async def consent_no(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.user_id:
            await interaction.response.send_message("This consent request is not for you.", ephemeral=True)
            return

        self.bot.user_consents[self.user_id] = False
        await interaction.response.send_message("❌ You have declined consent. You will not be included in recordings.", ephemeral=True)
        self.stop()

async def setup(bot):
    await bot.add_cog(RecordingCog(bot))
