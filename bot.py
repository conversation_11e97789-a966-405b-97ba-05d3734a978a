import discord
from discord.ext import commands
import asyncio
import os
import logging
from dotenv import load_dotenv
from datetime import datetime, timedelta
import json
from typing import Optional, Dict, List
import aiofiles

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class VoiceRecordingBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        intents.voice_states = True
        intents.guilds = True

        super().__init__(
            command_prefix='!',
            intents=intents,
            help_command=None
        )

        # Bot configuration
        self.token = os.getenv('DISCORD_TOKEN')
        self.guild_id = int(os.getenv('GUILD_ID', 0))
        self.default_output_channel = int(os.getenv('DEFAULT_OUTPUT_CHANNEL_ID', 0))

        # Recording settings
        self.default_format = os.getenv('DEFAULT_RECORDING_FORMAT', 'mp3')
        self.default_sample_rate = int(os.getenv('DEFAULT_SAMPLE_RATE', 48000))
        self.default_bitrate = int(os.getenv('DEFAULT_BITRATE', 128))
        self.max_duration = int(os.getenv('MAX_RECORDING_DURATION', 3600))

        # File settings
        self.recordings_dir = os.getenv('RECORDINGS_DIR', 'recordings')
        self.max_file_size = int(os.getenv('MAX_FILE_SIZE_MB', 25)) * 1024 * 1024

        # Privacy settings
        self.require_consent = os.getenv('REQUIRE_USER_CONSENT', 'true').lower() == 'true'
        self.auto_delete_hours = int(os.getenv('AUTO_DELETE_AFTER_HOURS', 24))

        # Active recordings tracking
        self.active_recordings: Dict[int, dict] = {}
        self.user_consents: Dict[int, bool] = {}

        # Ensure recordings directory exists
        os.makedirs(self.recordings_dir, exist_ok=True)

    async def setup_hook(self):
        """Called when the bot is starting up"""
        logger.info("Setting up bot...")

        # Load cogs
        await self.load_extension('cogs.recording')
        await self.load_extension('cogs.commands')

        # Sync slash commands
        if self.guild_id:
            guild = discord.Object(id=self.guild_id)
            self.tree.copy_global_to(guild=guild)
            await self.tree.sync(guild=guild)
            logger.info(f"Synced commands to guild {self.guild_id}")
        else:
            await self.tree.sync()
            logger.info("Synced commands globally")

    async def on_ready(self):
        """Called when the bot is ready"""
        logger.info(f'{self.user} has connected to Discord!')
        logger.info(f'Bot is in {len(self.guilds)} guilds')

        # Set bot status
        await self.change_presence(
            activity=discord.Activity(
                type=discord.ActivityType.listening,
                name="voice channels 🎙️"
            )
        )

    async def on_voice_state_update(self, member, before, after):
        """Handle voice state changes"""
        # Check if this affects any active recordings
        for channel_id, recording_info in self.active_recordings.items():
            if before.channel and before.channel.id == channel_id:
                # User left the recorded channel
                if member.id in recording_info.get('participants', []):
                    recording_info['participants'].remove(member.id)
                    logger.info(f"User {member.display_name} left recorded channel {before.channel.name}")

            if after.channel and after.channel.id == channel_id:
                # User joined the recorded channel
                if member.id not in recording_info.get('participants', []):
                    recording_info['participants'].append(member.id)
                    logger.info(f"User {member.display_name} joined recorded channel {after.channel.name}")

bot = VoiceRecordingBot()

if __name__ == "__main__":
    if not bot.token:
        logger.error("DISCORD_TOKEN not found in environment variables!")
        exit(1)

    try:
        bot.run(bot.token)
    except Exception as e:
        logger.error(f"Failed to start bot: {e}")
